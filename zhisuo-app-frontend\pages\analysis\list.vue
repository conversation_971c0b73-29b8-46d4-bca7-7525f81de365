<template>
	<view class="page">
		<!-- 导航栏 -->
		<view class="navbar">
			<view class="nav-left" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
			</view>
			<view class="nav-title">我的分析</view>
			<view class="nav-right"></view>
		</view>

		<!-- 分析列表 -->
		<view class="analysis-container">
			<view class="analysis-list">
				<view class="analysis-item" v-for="(item, index) in analysisList" :key="index" @click="viewAnalysisDetail(item)">
					<view class="analysis-content">
						<view class="analysis-title">{{ item.title }}</view>
						<view class="analysis-time">
							<uni-icons type="calendar" size="14" color="#999"></uni-icons>
							<text>{{ formatTime(item.createTime || item.create_time || item.createdAt) }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view class="load-more" v-if="hasMore" @click="loadMore">
				<text v-if="!loading">加载更多</text>
				<text v-else>加载中...</text>
			</view>

			<!-- 没有更多数据 -->
			<view class="no-more" v-if="!hasMore && analysisList.length > 0">
				<text>没有更多数据了</text>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-if="analysisList.length === 0 && !loading">
				<text>暂无分析记录</text>
			</view>
		</view>
	</view>
</template>

<script>
	import Api from '../../common/api.js';

	export default {
		data() {
			return {
				analysisList: [],
				currentPage: 0,
				pageSize: 10,
				hasMore: true,
				loading: false
			}
		},
		onLoad() {
			this.loadAnalysisList();
		},
		methods: {
			// 返回上一页
			goBack() {
				uni.navigateBack();
			},

			// 加载分析列表
			loadAnalysisList() {
				if (this.loading) return;
				
				this.loading = true;
				Api.request({
					url: '/v1/user/analysis',
					method: 'GET',
					data: {
						page: this.currentPage,
						size: this.pageSize
					}
				}).then(res => {
					if (res.data && res.data.code === 0) {
						const newData = res.data.data.content || [];
						if (this.currentPage === 0) {
							this.analysisList = newData;
						} else {
							this.analysisList = [...this.analysisList, ...newData];
						}
						
						// 判断是否还有更多数据
						this.hasMore = newData.length === this.pageSize;
					}
				}).catch(err => {
					console.error('获取分析列表失败:', err);
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					});
				}).finally(() => {
					this.loading = false;
				});
			},

			// 加载更多
			loadMore() {
				if (!this.hasMore || this.loading) return;
				this.currentPage++;
				this.loadAnalysisList();
			},

			// 查看分析详情
			viewAnalysisDetail(analysis) {
				const analysisId = analysis?.analysis_id || analysis?.analysisId || analysis?.id;
				if (!analysis || !analysisId) {
					uni.showToast({
						title: '分析ID不存在',
						icon: 'none'
					});
					return;
				}

				uni.navigateTo({
					url: `/pages/analysis/detail?id=${analysisId}`
				});
			},

			// 格式化时间
			formatTime(time) {
				if (!time) return '';
				const date = new Date(time);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				
				// 如果是今年，只显示月日时分
				const currentYear = new Date().getFullYear();
				if (year === currentYear) {
					return `${month}-${day} ${hours}:${minutes}`;
				} else {
					return `${year}-${month}-${day} ${hours}:${minutes}`;
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		background-color: #f2f5fc;
		min-height: 100vh;
	}

	/* 导航栏样式 */
	.navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		background-color: #fff;
		border-bottom: 1px solid #f0f0f0;

		.nav-left, .nav-right {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.nav-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
	}

	/* 分析容器 */
	.analysis-container {
		padding: 20rpx;
	}

	/* 分析列表样式 */
	.analysis-list {
		.analysis-item {
			background-color: #fff;
			padding: 30rpx;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.98);
				background-color: #f0f6ff;
			}

			.analysis-content {
				.analysis-title {
					font-size: 32rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 15rpx;
					line-height: 1.4;
				}

				.analysis-time {
					display: flex;
					align-items: center;
					font-size: 26rpx;
					color: #999;

					text {
						margin-left: 8rpx;
					}
				}
			}
		}
	}

	/* 加载更多样式 */
	.load-more {
		text-align: center;
		padding: 30rpx;
		color: #3264ED;
		font-size: 28rpx;
	}

	.no-more {
		text-align: center;
		padding: 30rpx;
		color: #999;
		font-size: 26rpx;
	}

	/* 空状态样式 */
	.empty-state {
		text-align: center;
		padding: 100rpx 20rpx;
		color: #999;
		font-size: 28rpx;
	}
</style>
